
# Template Structure


# FILE ORGANIZATION
```
templates/
├── {system_id}-a-{system_name}-{component_function}.md
├── {system_id}-b-{system_name}-{component_function}.md
└── {system_id}-c-{system_name}-{component_function}.md
```

# STRUCTURAL TEMPLATE SPECIFICATION
```
Each template is stored as a markdown (.md) file and follows this standardized three-part structure ("[Title] Interpretation text `{transformation}`"):

    1. [TITLE]:
       - Enclosed in square brackets `[]`, defining the template's purpose.
       - Should be concise, descriptive, and follow title case formatting
       - Examples: `[Instruction Converter]`, `[Essence Distillation]`
    2. [INTERPRETATION]:
       - Activating Essence for Autonomous System Adaptation
       - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).
       - Should clearly explain the template's function in natural language
       - Can include formatting like **bold**, *italic*, or other markdown elements
       - Provides context for human readers to understand the template's purpose
    3. [TRANSFORMATION]:
       - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.
       - Contains the structured representation of the transformation process
       - Uses a consistent semi-colon separated key-value format
```


# INSTRUCTION TEMPLATE SYNTAX
```
Instruction example (reference to demonstrate the "syntax" and illustrate the generalized concept):

    [Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`
```

<!-- #### Transformation Template Syntax -->
<!-- # RULES FOR THE SYSTEM -->

# TEMPLATE TRANSFORMATION RULES

The transformation component must follow this standardized format:
```
{role=<role_name>; input=[<input_params>]; process=[<process_steps>]; constraints=[<constraints>]; requirements=[<requirements>]; output={<output_format>}}
```





# HIERARCHICAL STRUCTURE
```
System ({system_id})
├── Template A ({component_function})
│   ├── [Title]
│   ├── Interpretation
│   └── `{Transformation}`
├── Template B ({component_function})
└── Template C ({component_function})
```

# TEMPLATE FORMAT
```
[Title] Interpretation Execute as: `{Transformation}`
  │      │              │         └─ Machine-parsable parameters
  │      │              └─ Standard connector phrase
  │      └─ Human-readable instructions
  └─ Template identifier
```

# COMPONENT VISUALIZATION

```
┌─ Title ───────────────────────────────────────────┐
│ [Instruction Converter]                           │
└──────────────────────────────────────────────────┬┘
                                                   │
┌─ Interpretation ─────────────────────────────┐   │
│ Your goal is not to **answer** the           │   │
│ input prompt, but to **rephrase** it,        │   │
│ and to do so by the parameters defined       │   │
│ *inherently* within this message.            │   │
│ Execute as:                                  │   │
└─────────────────────────────────────────────┬┘   │
                                              │    │
┌─ Transformation ─────────────────────────┐  │    │
│ {                                        │  │    │
│   role=instruction_converter;            │  │    │
│   input=[original_text:str];             │◄─┴────┘
│   process=[                              │
│     strip_first_person_references(),     │
│     convert_statements_to_directives(),  │
│     identify_key_actions(),              │
│     ...                                  │
│   ];                                     │
│   constraints=[                          │
│     deliver_clear_actionable_commands(), │
│     preserve_original_sequence(),        │
│     ...                                  │
│   ];                                     │
│   requirements=[                         │
│     remove_self_references(),            │
│     use_command_voice(),                 │
│     ...                                  │
│   ];                                     │
│   output={instruction_format:str}        │
│ }                                        │
└──────────────────────────────────────────┘
```

# TRANSFORMATION STRUCTURE
```
┌─ Role ──────────────────────────────────────┐
│ role={function_identifier}                  │
│ # Defines template's primary function       │
└────────────────────────────────────────────┬┘
                                             │
┌─ Input ─────────────────────────────────┐  │
│ input=[{parameter}:{type}]              │  │
│ # Specifies input parameters and types  │  │
└─────────────────────────────────────────┘  │
                                             │
┌─ Process ───────────────────────────────┐  │
│ process=[                               │  │
│   {operation_1}(),                      │  │
│   {operation_2}(),                      │◄─┘
│   ...                                   │
│ ]                                       │
│ # Defines processing operations         │
└─────────────────────────────────────────┘

┌─ Constraints ─────────────────────────────┐
│ constraints=[                             │
│   {constraint_1}(),                       │
│   {constraint_2}(),                       │
│   ...                                     │
│ ]                                         │
│ # Sets operational boundaries             │
└──────────────────────────────────────────┬┘
                                           │
┌─ Requirements ──────────────────────┐    │
│ requirements=[                      │    │
│   {requirement_1}(),                │    │
│   {requirement_2}(),                │    │
│   ...                               │    │
│ ]                                   │    │
│ # Defines mandatory behaviors       │    │
└────────────────────────────────────┬┘    │
                                     │     │
┌─ Output ─────────────────────┐     │     │
│ output={parameter:{type}}    │◄────┴─────┘
│ # Specifies return format    │
└──────────────────────────────┘
```

# METADATA
Template:
  keywords: "{keyword_1}|{keyword_2}|{keyword_3}"
  template_id: "{system_id}-{step}-{system_name}-{component_function}"

System:
  sequence_id: "{system_id}"
  steps: [
    "{system_id}-a-{system_name}-{component_function}",
    "{system_id}-b-{system_name}-{component_function}",
    ...
  ]

## Example Templates

```
[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.
`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`
```

```
[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.
`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`
```

```
[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:
`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`
```

```
[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`
```

```
[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`
```

---
