#!/usr/bin/env python3

#===================================================================
# IMPORTS
#===================================================================
import os
import re
import json
import glob
import sys
import datetime


#===================================================================
# BASE CONFIG
#===================================================================
class TemplateConfig:
    LEVEL = None
    FORMAT = None
    SOURCE_DIR = None

    # Sequence definition
    SEQUENCE = {
        "pattern": re.compile(r"(\d+)-([a-z])-(.+)"),
        "id_group": 1,
        "step_group": 2,
        "name_group": 3,
        "order_function": lambda step: ord(step) - ord('a')
    }

    # Content extraction patterns
    PATTERNS = {}

    # Path helpers
    @classmethod
    def get_output_filename(cls):
        if not cls.FORMAT or not cls.LEVEL:
            raise NotImplementedError("FORMAT/LEVEL required.")
        return f"{cls.LEVEL}.{cls.FORMAT}.templates.json"

    @classmethod
    def get_full_output_path(cls, script_dir):
        return os.path.join(script_dir, cls.get_output_filename())

    @classmethod
    def get_full_source_path(cls, script_dir):
        if not cls.SOURCE_DIR:
            raise NotImplementedError("SOURCE_DIR required.")
        return os.path.join(script_dir, cls.SOURCE_DIR)


#===================================================================
# FORMAT: MARKDOWN (lvl1)
#===================================================================
class TemplateConfigMD(TemplateConfig):
    LEVEL = "lvl1"
    FORMAT = "md"
    SOURCE_DIR = "md"

    # Keywords to extract from interpretation text
    KEYWORDS_TO_MATCH = [
        "distill", "inherent", "maximally", "maximum", "clarity",
        "precision", "essence", "coherence", "structure", "elegant",
        "resonance", "amplification", "self", "transformation", "potency",
        "recursive", "adaptive", "meta", "synthesis", "potent",
        "totally", "completely",
        # Add other important semantic markers as needed
    ]

    # Combined pattern for lvl1 markdown templates
    _LVL1_MD_PATTERN = re.compile(
        r"\[(.*?)\]"     # Group 1: Title
        r"\s*"           # Match (but don't capture) whitespace AFTER title
        r"(.*?)"         # Group 2: Capture Interpretation text
        r"\s*"           # Match (but don't capture) whitespace BEFORE transformation
        r"(`\{.*?\}`)"   # Group 3: Transformation
    )

    PATTERNS = {
        "title": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(1).strip() if m else ""
        },
        "interpretation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(2).strip() if m else ""
        },
        "transformation": {
            "pattern": _LVL1_MD_PATTERN,
            "default": "",
            "extract": lambda m: m.group(3).strip() if m else ""
        }
    }


#===================================================================
# HELPERS
#===================================================================
def _extract_field(content, pattern_cfg):
    try:
        match = pattern_cfg["pattern"].search(content)
        return pattern_cfg["extract"](match)
    except Exception:
        return pattern_cfg.get("default", "")


def extract_keywords(text, keywords):
    """Extract keywords from text and return pipe-delimited string."""
    if not text or not keywords:
        return ""

    matches = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword.lower() in text_lower:
            matches.append(keyword)

    return "|".join(matches) if matches else ""


def _is_extraction_failed(parts):
    """Check if the extraction failed and we should use fallback."""
    title = parts.get("title", "").strip()
    interpretation = parts.get("interpretation", "").strip()
    transformation = parts.get("transformation", "").strip()

    # Check for generic/placeholder content that indicates failed extraction
    generic_indicators = ["Title", "Interpretation", "Transformation", "Execute as:", "`{Transformation}`"]

    # Failed if any part is empty
    if not title or not interpretation or not transformation:
        return True

    # Failed if any part is just a generic placeholder
    if any(part.strip() in generic_indicators for part in [title, interpretation, transformation]):
        return True

    # Failed if interpretation is too short (likely not the real content)
    if len(interpretation) < 50:
        return True

    return False


def _create_fallback_parts(content, template_id):
    """Create fallback parts when regex extraction fails."""
    # Try to extract title from first line if it has brackets
    lines = content.split('\n')
    first_line = lines[0].strip() if lines else ""

    # Extract title from [Title] pattern if present
    title_match = re.match(r'\[(.*?)\]', first_line)
    if title_match:
        title = title_match.group(1).strip()
        # Use the rest of the content as interpretation
        remaining_content = '\n'.join(lines[1:]).strip() if len(lines) > 1 else content
    else:
        # Use template_id as title and full content as interpretation
        title = template_id.replace('-', ' ').replace('_', ' ').title()
        remaining_content = content

    # Leave transformation empty in fallback mode
    transformation = ""

    return {
        "title": title,
        "interpretation": remaining_content,
        "transformation": transformation
    }


def extract_metadata(content, template_id, config):
    content = content.strip()
    parts = {k: _extract_field(content, v) for k, v in config.PATTERNS.items()}

    # Check if extraction failed and use fallback if needed
    if _is_extraction_failed(parts):
        print(f"INFO: Using fallback extraction for {template_id}")
        parts = _create_fallback_parts(content, template_id)

    # Extract keywords from interpretation text
    if hasattr(config, 'KEYWORDS_TO_MATCH'):
        interpretation = parts.get("interpretation", "")
        if interpretation:
            keywords = extract_keywords(interpretation, config.KEYWORDS_TO_MATCH)
            parts["keywords"] = keywords

    return {"raw": content, "parts": parts}


#===================================================================
# CATALOG GENERATION
#===================================================================
def generate_catalog(config, script_dir):
    # Find templates and extract metadata
    source_path = config.get_full_source_path(script_dir)
    template_files = glob.glob(os.path.join(source_path, f"*.{config.FORMAT}"))

    print(f"\n--- Generating Catalog: {config.LEVEL} / {config.FORMAT} ---")
    print(f"Source: {source_path} (*.{config.FORMAT})")
    print(f"Found {len(template_files)} template files.")

    templates = {}
    sequences = {}

    # Process each template file
    for file_path in template_files:
        filename = os.path.basename(file_path)
        template_id = os.path.splitext(filename)[0]

        try:
            # Read content with proper UTF-8 encoding
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()

            # Extract and store template metadata
            template_data = extract_metadata(content, template_id, config)
            templates[template_id] = template_data

            # Process sequence information from filename
            seq_match = config.SEQUENCE["pattern"].match(template_id)
            if seq_match:
                seq_id = seq_match.group(config.SEQUENCE["id_group"])
                step = seq_match.group(config.SEQUENCE["step_group"])
                seq_order = config.SEQUENCE["order_function"](step)
                sequences.setdefault(seq_id, []).append({
                    "template_id": template_id, "step": step, "order": seq_order
                })
            print(f"SUCCESS: Processed {template_id}")
        except Exception as e:
            print(f"ERROR: {template_id} -> {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()

    # Sort sequence steps
    for seq_id, steps in sequences.items():
        try:
            steps.sort(key=lambda step: step["order"])
        except Exception as e:
            print(f"WARN: Failed to sort sequence {seq_id}: {e}", file=sys.stderr)

    # Create catalog with metadata
    timestamp = datetime.datetime.now().strftime("%Y.%m.%d-kl.%H.%M")
    return {
        "catalog_meta": {
            "level": config.LEVEL,
            "format": config.FORMAT,
            "generated_at": timestamp,
            "source_directory": config.SOURCE_DIR,
            "total_templates": len(templates),
            "total_sequences": len(sequences)
        },
        "templates": templates,
        "sequences": sequences
    }


def save_catalog(catalog_data, config, script_dir):
    output_path = config.get_full_output_path(script_dir)
    print(f"Output: {output_path}")

    try:
        with open(output_path, 'w', encoding='utf-8', errors='replace') as f:
            json.dump(catalog_data, f, indent=2, ensure_ascii=False)
            print(f"SUCCESS: Saved catalog with {catalog_data['catalog_meta']['total_templates']} templates.")
            print("--- Catalog Generation Complete ---")
        return output_path
    except Exception as e:
        print(f"ERROR: Failed to save catalog: {e}", file=sys.stderr)
        return None


#===================================================================
# IMPORTABLE API FOR EXTERNAL SCRIPTS
#===================================================================
def get_default_catalog_path(script_dir=None):
    """Get the path to the default catalog file."""
    if script_dir is None:
        script_dir = os.path.dirname(os.path.abspath(__file__))
    return TemplateConfigMD().get_full_output_path(script_dir)


def load_catalog(catalog_path=None):
    """Load a catalog from a JSON file."""
    if catalog_path is None:
        catalog_path = get_default_catalog_path()

    if not os.path.exists(catalog_path):
        raise FileNotFoundError(f"Catalog file not found: {catalog_path}")

    try:
        with open(catalog_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        raise ValueError(f"Invalid JSON in catalog file: {catalog_path}")


def get_template(catalog, template_id):
    """Get a template by its ID."""
    if "templates" not in catalog or template_id not in catalog["templates"]:
        return None
    return catalog["templates"][template_id]


def get_sequence(catalog, sequence_id):
    """Get all templates in a sequence, ordered by steps."""
    if "sequences" not in catalog or sequence_id not in catalog["sequences"]:
        return []

    sequence_steps = catalog["sequences"][sequence_id]
    return [(step["step"], get_template(catalog, step["template_id"]))
            for step in sequence_steps]


def get_all_sequences(catalog):
    """Get a list of all sequence IDs."""
    if "sequences" not in catalog:
        return []
    return list(catalog["sequences"].keys())


def get_system_instruction(template):
    """Convert a template to a system instruction format."""
    if not template or "parts" not in template:
        return None

    parts = template["parts"]
    instruction = f"# {parts.get('title', '')}\n\n"

    if "interpretation" in parts and parts["interpretation"]:
        instruction += f"{parts['interpretation']}\n\n"

    if "transformation" in parts and parts["transformation"]:
        instruction += parts["transformation"]

    return instruction


def regenerate_catalog(force=False):
    """Regenerate the catalog file."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config = TemplateConfigMD()
    catalog_path = config.get_full_output_path(script_dir)

    if os.path.exists(catalog_path) and not force:
        # Check if catalog is older than any template file
        catalog_mtime = os.path.getmtime(catalog_path)
        templates_dir = config.get_full_source_path(script_dir)

        needs_update = False
        for file_path in glob.glob(os.path.join(templates_dir, f"*.{config.FORMAT}")):
            if os.path.getmtime(file_path) > catalog_mtime:
                needs_update = True
                break

        if not needs_update:
            # print("Catalog is up to date")
            return load_catalog(catalog_path)

    # Generate and save new catalog
    catalog = generate_catalog(config, script_dir)
    save_catalog(catalog, config, script_dir)
    return catalog


#===================================================================
# SCRIPT EXECUTION
#===================================================================
if __name__ == "__main__":
    SCRIPT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))

    # Check for command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--api-test":
        # Simple API test
        catalog = regenerate_catalog()
        print("\nAvailable sequences:")
        for seq_id in get_all_sequences(catalog):
            sequence_steps = get_sequence(catalog, seq_id)
            if sequence_steps:
                first_step_title = sequence_steps[0][1]["parts"]["title"]
                print(f"  {seq_id}: {first_step_title} ({len(sequence_steps)} steps)")

        sys.exit(0)

    # Regular execution
    try:
        config_to_use = TemplateConfigMD
        active_config = config_to_use()
        catalog = generate_catalog(active_config, SCRIPT_DIRECTORY)
        save_catalog(catalog, active_config, SCRIPT_DIRECTORY)

    except NotImplementedError as e:
        print(f"CONFIG ERROR: {config_to_use.__name__} is incomplete: {e}", file=sys.stderr)
        sys.exit(1)
    except FileNotFoundError as e:
        print(f"FILE ERROR: Source directory not found: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"FATAL ERROR: {e}", file=sys.stderr)
        sys.exit(1)
