# Template Specification Guide

## Overview

This document defines the complete specification for creating AI instruction templates in the Template-Based Instruction Processing System. Templates follow a standardized three-part structure optimized for LLM performance and human readability.

## Core Template Structure

Every template MUST follow this exact format:

```
[Title] Interpretation Execute as: `{Transformation}`
```

### Three-Part Breakdown

#### 1. **[Title]** - Template Identifier
- **Format**: Enclosed in square brackets `[Title]`
- **Purpose**: Concise description of template's function
- **Style**: Title case, descriptive, action-oriented
- **Examples**: `[Instruction Converter]`, `[Essence Distillation]`, `[Code Optimizer]`

#### 2. **Interpretation** - Human-Readable Instructions
- **Format**: Plain text following the title
- **Pattern**: MUST begin with goal negation: `"Your goal is not to **[action]**, but to **[transformation]**"`
- **Purpose**: Explains template function in natural language
- **Style**: Command voice, no self-reference, clear directives
- **Ending**: MUST end with "Execute as:" leading to transformation block

#### 3. **`{Transformation}`** - Machine-Parsable Parameters
- **Format**: JSON-like structure in backticks with curly braces
- **Purpose**: Structured execution parameters for LLM processing
- **Components**: role, input, process, constraints, requirements, output

## Transformation Block Specification

The transformation block follows this mandatory structure:

```
{
  role=<role_name>;
  input=[<input_params>];
  process=[<process_steps>];
  constraints=[<constraints>];
  requirements=[<requirements>];
  output={<output_format>}
}
```

### Component Details

#### **role** (Required)
- **Purpose**: Defines the functional role of the template
- **Format**: `role=<specific_role_name>`
- **Rules**: 
  - Must be specific and descriptive
  - No generic roles like "assistant" or "helper"
  - Use underscore_case for multi-word roles
- **Examples**: `role=essence_distiller`, `role=code_optimizer`, `role=instruction_converter`

#### **input** (Required)
- **Purpose**: Specifies expected input format and parameters
- **Format**: `input=[<parameter_name>:<data_type>]`
- **Rules**:
  - Use descriptive parameter names
  - Include data type specification
  - Support multiple parameters with comma separation
- **Examples**: 
  - `input=[text:str]`
  - `input=[code:str, language:str]`
  - `input=[original:any]`

#### **process** (Required)
- **Purpose**: Defines ordered processing steps
- **Format**: `process=[<step1>(), <step2>(), ...]`
- **Rules**:
  - Use function-like notation with parentheses
  - Steps must be actionable and atomic
  - Maintain logical sequence order
  - Use descriptive, verb-based names
- **Examples**:
  ```
  process=[
    identify_core_intent(),
    strip_non_essential_elements(),
    determine_optimal_structure(),
    validate_essence_preservation()
  ]
  ```

#### **constraints** (Optional)
- **Purpose**: Specifies limitations and boundaries
- **Format**: `constraints=[<constraint1>(), <constraint2>(), ...]`
- **Rules**:
  - Define operational boundaries
  - Specify format or style limitations
  - Include scope restrictions
- **Examples**:
  ```
  constraints=[
    preserve_original_meaning(),
    maintain_technical_accuracy(),
    limit_output_length(max_words=100)
  ]
  ```

#### **requirements** (Optional)
- **Purpose**: Defines mandatory output characteristics
- **Format**: `requirements=[<requirement1>(), <requirement2>(), ...]`
- **Rules**:
  - Specify quality standards
  - Define format requirements
  - Include validation criteria
- **Examples**:
  ```
  requirements=[
    structured_output(),
    type_safety(),
    comprehensive_coverage()
  ]
  ```

#### **output** (Required)
- **Purpose**: Specifies return format and structure
- **Format**: `output={<parameter_name>:<data_type>}`
- **Rules**:
  - Use descriptive parameter names
  - Include data type specification
  - Support complex output structures
- **Examples**:
  - `output={enhanced_prompt:str}`
  - `output={analysis:dict}`
  - `output={optimized_code:str, improvements:list}`

## Template Examples

### Example 1: Simple Transformation Template
```markdown
[Text Summarizer] Your goal is not to **repeat** the input text, but to **distill** it into its essential points while preserving key information. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary(), validate_completeness()]; constraints=[preserve_critical_information(), maintain_original_tone()]; requirements=[concise_output(), factual_accuracy()]; output={summary:str}}`
```

### Example 2: Multi-Parameter Template
```markdown
[Code Refactor] Your goal is not to **rewrite** the code arbitrarily, but to **optimize** it for readability and performance while maintaining functionality. Execute as: `{role=code_optimizer; input=[source_code:str, language:str, optimization_goals:list]; process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality(), generate_improvements()]; constraints=[preserve_behavior(), maintain_compatibility()]; requirements=[improved_readability(), measurable_performance_gains()]; output={refactored_code:str, optimization_report:dict}}`
```

### Example 3: Analysis Template
```markdown
[Content Analyzer] Your goal is not to **describe** the content superficially, but to **analyze** its structure, themes, and effectiveness systematically. Execute as: `{role=content_analyst; input=[content:any]; process=[parse_structure(), identify_themes(), evaluate_effectiveness(), assess_clarity(), generate_insights()]; constraints=[objective_analysis(), evidence_based_conclusions()]; requirements=[comprehensive_coverage(), actionable_insights()]; output={analysis:dict}}`
```

## File Naming Convention

Templates follow a structured naming pattern that enables automatic organization:

### Format
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Components

#### **sequence_id** (Required)
- **Format**: Four-digit number with leading zeros
- **Purpose**: Groups related templates into sequences
- **Examples**: `0001`, `0002`, `1010`

#### **step** (Optional)
- **Format**: Single lowercase letter (a, b, c, d, e, ...)
- **Purpose**: Indicates position within a sequence
- **Rules**: Alphabetical order determines execution sequence
- **Usage**: Only for multi-step sequences

#### **descriptive_name** (Required)
- **Format**: Hyphenated lowercase words
- **Purpose**: Describes template's specific function
- **Rules**: Concise but descriptive, no spaces
- **Examples**: `instruction-converter`, `essence-distillation`, `code-optimizer`

### Naming Examples

#### Standalone Templates
- `0001-instruction-converter.md`
- `0005-text-summarizer.md`
- `0010-code-analyzer.md`

#### Sequential Templates
- `0002-a-essence-distillation.md`
- `0002-b-coherence-enhancement.md`
- `0002-c-precision-optimization.md`
- `0002-d-structured-transformation.md`

## Compliance Rules

### ✅ Required Elements
1. **Three-part structure**: Title, Interpretation, Transformation
2. **Goal negation pattern**: "Your goal is not to X, but to Y"
3. **Command voice**: No first-person references or conversational language
4. **Typed parameters**: All inputs and outputs must specify data types
5. **Actionable processes**: Function-like steps that are specific and executable
6. **Structured output**: Well-defined return format

### ❌ Forbidden Practices

#### Language Violations
- First-person references: *I, me, my, we, us*
- Conversational phrases: *please, thank you, let's*
- Uncertain language: *maybe, perhaps, might, could*
- Question forms in directives
- Explanatory justifications or meta-commentary

#### Structural Violations
- Merging or omitting required sections
- Untyped parameters or outputs
- Generic roles like "assistant" or "helper"
- Vague or unstructured process descriptions
- Missing goal negation pattern

#### Output Violations
- Conversational or meta-commentary
- Self-referential language
- Unstructured or loosely formatted results
- Missing type specifications

## Validation Checklist

Before finalizing any template, verify:

- [ ] Three-part structure is intact
- [ ] Goal negation is present and properly formatted
- [ ] Role is specific and non-generic
- [ ] Input parameters are typed
- [ ] Process steps are ordered and actionable
- [ ] Constraints and requirements are specified (if applicable)
- [ ] Output format is typed and structured
- [ ] No forbidden language patterns are used
- [ ] File naming convention is followed
- [ ] Template serves a clear, specific purpose

## Advanced Features

### Keyword Extraction
Templates automatically extract semantic keywords from interpretation text for cataloging and search functionality. Key terms include:
- `distill`, `essence`, `maximally`, `precision`
- `coherence`, `structure`, `elegant`, `transformation`
- `recursive`, `adaptive`, `meta`, `synthesis`

### Sequence Composition
Templates can be composed into complex workflows:
- **Linear Sequences**: A→B→C progression
- **Branching Sequences**: Multiple parallel approaches
- **Aggregation Sequences**: Combining multiple inputs
- **Conditional Sequences**: Dynamic step selection

### Template Inheritance
Advanced templates can inherit properties from base templates while adding specialized functionality.

## Best Practices

1. **Clarity First**: Ensure template purpose is immediately clear
2. **Atomic Operations**: Each template should perform one specific transformation
3. **Composability**: Design templates to work well in sequences
4. **Type Safety**: Always specify data types for inputs and outputs
5. **Validation**: Include validation steps in process flows
6. **Documentation**: Use descriptive names and clear process steps
7. **Testing**: Validate templates with representative inputs
8. **Optimization**: Refine based on actual LLM performance

## Integration with Catalog System

Templates are automatically processed by the catalog generation system which:
- Extracts metadata using regex patterns
- Organizes templates into sequences
- Generates searchable catalogs
- Validates template compliance
- Enables dynamic template discovery

For successful integration, templates must strictly adhere to the format specification outlined in this document.
