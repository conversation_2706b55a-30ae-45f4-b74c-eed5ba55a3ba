# Directional Templates Usage Guide

## Quick Start

Directional templates provide **universal transformation vectors** that work with any input. Instead of analyzing content, you apply directional operations like `amplify`, `clarify`, `distill`, or `expand`.

## Basic Usage

### Single Vector Application
```bash
# Amplify any input
python src/lvl1/lvl1_sequence_executor.py --sequence 9000-a-amplify "Make this stronger"

# Clarify any input  
python src/lvl1/lvl1_sequence_executor.py --sequence 9001-a-clarify "Complex technical document"

# Distill any input
python src/lvl1/lvl1_sequence_executor.py --sequence 9003-b-distill "Long rambling explanation"
```

### Vector Chaining
```bash
# Distill → Amplify → Clarify
python src/lvl1/lvl1_sequence_executor.py \
  --sequence "9003-b-distill|9000-a-amplify|9001-a-clarify" \
  --chain-mode \
  "Any complex input"
```

## Core Directional Vectors

### 🔥 Intensity Vectors (9000 Series)

#### Amplify (9000-a)
**Purpose**: Intensify inherent qualities
**Works with**: Ideas, emotions, arguments, code, designs
**Example**: 
- Input: "This is a good solution"
- Output: "This is an exceptionally powerful and transformative solution"

#### Intensify (9000-b)  
**Purpose**: Compress to maximum density
**Works with**: Concepts, code, processes, communications
**Example**:
- Input: "We should consider improving our approach"
- Output: "OPTIMIZE APPROACH IMMEDIATELY"

#### Diminish (9000-c)
**Purpose**: Reduce intensity while preserving form
**Works with**: Aggressive text, overwhelming data, complex systems
**Example**:
- Input: "This is absolutely critical and must be done now!"
- Output: "This is important and should be addressed soon"

### 🔍 Clarity Vectors (9001 Series)

#### Clarify (9001-a)
**Purpose**: Enhance transparency and definition
**Works with**: Unclear instructions, complex code, vague concepts
**Example**:
- Input: "The thing needs to be fixed somehow"
- Output: "The authentication module requires debugging of the token validation logic"

#### Purify (9001-b)
**Purpose**: Remove non-essential elements
**Works with**: Cluttered text, redundant code, mixed messages
**Example**:
- Input: "Well, I think maybe we should probably consider possibly improving this"
- Output: "Improve this"

### 📐 Structural Vectors (9002 Series)

#### Expand (9002-a)
**Purpose**: Extend natural boundaries
**Works with**: Brief ideas, minimal code, compressed data
**Example**:
- Input: "Use AI"
- Output: "Implement artificial intelligence systems including machine learning algorithms, natural language processing, and automated decision-making frameworks"

#### Compress (9002-b)
**Purpose**: Maximize density without loss
**Works with**: Verbose text, redundant code, lengthy explanations
**Example**:
- Input: "In order to accomplish the task of data processing, we need to implement..."
- Output: "Process data via implementation of..."

### ⚡ Transformation Vectors (9003 Series)

#### Distill (9003-b)
**Purpose**: Extract absolute essence
**Works with**: Any complex input
**Example**:
- Input: "After careful consideration of multiple factors and extensive analysis..."
- Output: "Core insight: [essential point]"

#### Elevate (9003-a)
**Purpose**: Transform to higher operational level
**Works with**: Basic concepts, simple code, elementary ideas
**Example**:
- Input: "Add numbers together"
- Output: "Implement mathematical aggregation with error handling, type validation, and performance optimization"

### 🧠 Meta Vectors (9004 Series)

#### Abstract (9004-a)
**Purpose**: Extract pure conceptual form
**Works with**: Specific examples, concrete implementations, particular cases
**Example**:
- Input: "Fix the login button on the homepage"
- Output: "Resolve user interface interaction failures in authentication workflows"

## Practical Applications

### Content Enhancement
```bash
# Make any text more impactful
--sequence "9003-b-distill|9000-a-amplify|9001-a-clarify"

# Simplify complex content
--sequence "9001-b-purify|9002-b-compress|9001-a-clarify"

# Expand brief ideas
--sequence "9002-a-expand|9001-a-clarify|9000-a-amplify"
```

### Code Processing
```bash
# Optimize code structure
--sequence "9003-b-distill|9002-c-restructure|9001-a-clarify"

# Enhance code documentation
--sequence "9004-a-abstract|9002-a-expand|9001-a-clarify"

# Compress verbose code
--sequence "9001-b-purify|9002-b-compress|9003-b-distill"
```

### Problem Solving
```bash
# Clarify complex problems
--sequence "9004-a-abstract|9001-a-clarify|9003-b-distill"

# Expand solution space
--sequence "9002-a-expand|9004-a-abstract|9003-c-synthesize"

# Focus scattered thinking
--sequence "9001-b-purify|9003-b-distill|9000-a-amplify"
```

## Vector Combinations

### Essence Extraction Pipeline
```
Input → Distill → Amplify → Clarify → Pure Essence
```
**Use case**: Extract key insights from complex documents

### Expansion Pipeline  
```
Input → Abstract → Expand → Concretize → Detailed Implementation
```
**Use case**: Develop brief ideas into comprehensive plans

### Optimization Pipeline
```
Input → Purify → Compress → Restructure → Optimized Form
```
**Use case**: Streamline complex processes or code

### Clarity Pipeline
```
Input → Clarify → Purify → Amplify → Crystal Clear Output
```
**Use case**: Make confusing content perfectly clear

## Advanced Patterns

### Recursive Application
```bash
# Apply amplify to the amplification process itself
--sequence "9000-a-amplify" "amplify this text"
# Then apply amplify again to the result
```

### Parallel Processing
```bash
# Apply multiple vectors to same input
--sequence "9000-a-amplify|9001-a-clarify|9003-b-distill" --no-chain-mode
```

### Conditional Vectors
```bash
# Use different vectors based on input characteristics
if [input_is_unclear]: use 9001-a-clarify
if [input_is_verbose]: use 9002-b-compress  
if [input_is_weak]: use 9000-a-amplify
```

## Context-Free Examples

### Any Text Input
```
"The weather is nice today"
→ Amplify: "The weather is absolutely magnificent and perfect today"
→ Clarify: "Today features pleasant atmospheric conditions"
→ Distill: "Good weather"
→ Expand: "Today's meteorological conditions include optimal temperature, humidity, and atmospheric pressure creating an exceptionally pleasant environmental experience"
```

### Any Code Input
```python
def add(a, b):
    return a + b

# Amplify
def performHighPrecisionMathematicalAddition(primaryOperand, secondaryOperand):
    return primaryOperand + secondaryOperand

# Clarify  
def add_two_numbers(first_number, second_number):
    """Add two numbers and return the sum."""
    return first_number + second_number

# Distill
lambda a,b: a+b

# Expand
def add_with_validation(a, b):
    if not isinstance(a, (int, float)) or not isinstance(b, (int, float)):
        raise TypeError("Arguments must be numeric")
    result = a + b
    return result
```

### Any Concept Input
```
"Leadership"
→ Amplify: "Transformational visionary leadership that inspires and empowers"
→ Clarify: "The ability to guide and influence others toward common goals"
→ Distill: "Influence"
→ Abstract: "Directional social force"
→ Expand: "Leadership encompasses vision-setting, team motivation, strategic decision-making, communication excellence, and adaptive management across diverse organizational contexts"
```

## Best Practices

### 1. Start Simple
Begin with single vectors before chaining:
```bash
--sequence 9001-a-clarify "unclear input"
```

### 2. Chain Logically
Ensure vector sequences make sense:
```bash
# Good: distill → amplify → clarify
# Avoid: amplify → diminish (contradictory)
```

### 3. Match Vector to Need
- **Unclear input**: Use clarify
- **Weak impact**: Use amplify  
- **Too verbose**: Use compress
- **Too brief**: Use expand
- **Complex**: Use distill

### 4. Test Combinations
Experiment with different vector sequences:
```bash
# Try different approaches
--sequence "9003-b-distill|9000-a-amplify"
--sequence "9001-a-clarify|9002-a-expand"  
--sequence "9004-a-abstract|9003-c-synthesize"
```

### 5. Use Chain Mode
For sequential transformation:
```bash
--chain-mode  # Output of step N becomes input of step N+1
```

## Troubleshooting

### Vector Not Working
- Check template file exists in `src/lvl1/templates/lvl1/md/`
- Verify sequence ID format (e.g., `9000-a-amplify`)
- Regenerate catalog if needed

### Unexpected Results
- Try single vector first before chaining
- Check if vectors are compatible
- Consider input type and vector purpose

### Performance Issues
- Use fewer vectors in chain
- Apply vectors to smaller input chunks
- Consider parallel processing instead of chaining

## Quick Reference

| Vector | ID | Purpose | Best For |
|--------|----|---------| ---------|
| Amplify | 9000-a | Intensify | Weak content |
| Clarify | 9001-a | Enhance clarity | Unclear content |
| Distill | 9003-b | Extract essence | Complex content |
| Expand | 9002-a | Extend boundaries | Brief content |
| Abstract | 9004-a | Conceptualize | Specific content |
| Compress | 9002-b | Maximize density | Verbose content |
| Purify | 9001-b | Remove non-essential | Cluttered content |
| Elevate | 9003-a | Higher level | Basic content |

Remember: **Directional vectors work with ANY input** - no analysis required, just apply the transformation!
