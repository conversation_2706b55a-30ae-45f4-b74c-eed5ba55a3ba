## ✅ **MINIFIED + HIGH-<PERSON><PERSON>ITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**

# Universal Instruction Processing Protocol (Token-Efficient Edition)

## CORE FORMAT

**All templates MUST follow this fixed structure:**
```
[Title] Interpretation Execute as: `{Transformation}`
```

### STRUCTURE RULES
- No section merging or omission.
- Start interpretation with:
  `"Your goal is not to **[action]**, but to **[transformation]**"`
- Use **command voice only**; no I/me/we/please/etc.

### TRANSFORMATION BLOCK FORMAT:
```
{
  role=[specific:role];
  input=[name:type];
  process=[ordered_function_calls()];
  constraints=[scope_limits()];
  requirements=[output_spec];
  output={name:type}
}
```

---

## MANDATORY COMPLIANCE RULES

### ✅ REQUIRED
- 3-part structure
- Typed parameters
- Ordered, atomic processes
- Output format must be structured
- No conversational or explanatory text

### ❌ FORBIDDEN
- Explanations, justifications, or questions
- Generic roles (e.g., “assistant”)
- Vague, non-actionable process steps

---

## VALIDATION CHECKLIST (Boolean Logic)
```json
{
  "structure_compliant": true,
  "goal_negation_present": true,
  "role_specified": true,
  "input_typed": true,
  "process_actionable": true,
  "constraints_limited": true,
  "requirements_explicit": true,
  "output_typed": true,
  "forbidden_language_absent": true
}
```

---

## SAMPLE: MINIMAL VALID TEMPLATE

```md
[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`
```

---

## FINAL LAW

**Deviation = rejection.**
**Compliance = propagation.**
**Execute accordingly.**
